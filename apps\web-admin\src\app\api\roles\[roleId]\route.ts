/**
 * API Route: Get Role by ID
 * 
 * This route fetches role data from the roles collection
 * to support admin authentication with the existing schema.
 */

import { NextRequest, NextResponse } from 'next/server';
import { getAdminFirestore } from '@/lib/firebase-admin';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ roleId: string }> }
) {
  try {
    const { roleId } = await params;

    if (!roleId) {
      return NextResponse.json(
        { error: 'Role ID is required' },
        { status: 400 }
      );
    }

    // Get Firestore instance
    const db = getAdminFirestore();

    // Get role document
    const roleDoc = await db.collection('roles').doc(roleId).get();

    if (!roleDoc.exists) {
      return NextResponse.json(
        { error: 'Role not found' },
        { status: 404 }
      );
    }

    const roleData = roleDoc.data();

    // Return role data
    return NextResponse.json({
      id: roleDoc.id,
      name: roleData?.name,
      description: roleData?.description,
      level: roleData?.level,
      permissions: roleData?.permissions,
      isActive: roleData?.isActive,
      isSystemRole: roleData?.isSystemRole,
      canManageContent: roleData?.canManageContent,
      canManageRoles: roleData?.canManageRoles,
      canManageSettings: roleData?.canManageSettings,
      canManageUsers: roleData?.canManageUsers,
      userCount: roleData?.userCount,
      createdAt: roleData?.createdAt?.toDate?.()?.toISOString(),
      updatedAt: roleData?.updatedAt?.toDate?.()?.toISOString(),
    });

  } catch (error) {
    console.error('Error fetching role:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
