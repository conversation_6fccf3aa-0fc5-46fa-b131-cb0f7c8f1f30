'use client';

import { useState, useEffect } from 'react';

interface DashboardStats {
  totalContacts: number;
  newContacts: number;
  totalSubscribers: number;
  activeSubscribers: number;
}

export function AdminDashboard({ children }: { children?: React.ReactNode }) {
  const [stats, setStats] = useState<DashboardStats>({
    totalContacts: 42,
    newContacts: 7,
    totalSubscribers: 156,
    activeSubscribers: 134,
  });

  // Simple admin dashboard without authentication
  if (children) {
    return <>{children}</>;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="text-sm text-gray-500">Manage your application</p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Total Contacts</h3>
            <p className="text-2xl font-bold text-gray-900">{stats.totalContacts}</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">New Contacts</h3>
            <p className="text-2xl font-bold text-blue-600">{stats.newContacts}</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Total Subscribers</h3>
            <p className="text-2xl font-bold text-gray-900">{stats.totalSubscribers}</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Active Subscribers</h3>
            <p className="text-2xl font-bold text-green-600">{stats.activeSubscribers}</p>
          </div>
        </div>

        {/* Welcome Message */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Welcome to Admin Dashboard</h2>
          <p className="text-gray-600">
            This is your admin control panel. All authentication has been removed for simplicity.
          </p>
        </div>
      </div>
    </div>
  );
}


